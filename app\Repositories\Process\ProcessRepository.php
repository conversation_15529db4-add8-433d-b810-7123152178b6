<?php
namespace App\Repositories\Process;

use App\Repositories\EloquentRepository;
use App\Enums\ProcessStatus;
use App\Repositories\User\UserRepositoryInterface;
use App\Services\JobPermissionService;
use Illuminate\Support\Facades\Auth;

class ProcessRepository extends EloquentRepository implements ProcessRepositoryInterface
{
	private $userRepository;
	private $jobPermissionService;

	public function __construct(
		UserRepositoryInterface $userRepository, 
		JobPermissionService $jobPermissionService
	)
	{
		parent::__construct();
		$this->userRepository = $userRepository;
		$this->jobPermissionService = $jobPermissionService;
	}

	public function getModel()
	{
		return \App\Models\Process::class;
	}

	public function getWorkflowByScopeUses($search)
	{
		$select_columns = [
			'id',
			'name',
			'status',
		];

		$workflows = $this->model
			->select($select_columns)
			->with(['processVersionActive:id,process_id,form_id,scope_use,job_manager,followers'])
			->when(($search), function($query) use ($search) {
				$query->where('name', 'LIKE', '%' . $search . '%');
			})
			->where('status', ProcessStatus::ACTIVE->value)
			->limit(20)
			->get();

		// Xử lý where với cột scope_use để những user nào có quyền mới được sử dụng
		$userId = auth()->id(); // Lấy ID của user đang đăng nhập
        $workflows = $workflows->filter(function($workflow) use ($userId) {
            $scopeUses = $workflow->processVersionActive->scope_use;
            if (is_null($scopeUses)) {
                return true; // Nếu scope_use là null, mặc định được lấy
            }
            $usersInScope = $this->userRepository->getUserByOptionScopeRes($scopeUses, $workflow->id);
            return in_array($userId, array_column($usersInScope, 'value'));
        });
		// dd($workflows);
		return $workflows;	
	}

	public function getAllWorkflowRes($dataSearch)
	{
		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab'])  
			? ProcessStatus::fromString($dataSearch['tab']) 
			: ProcessStatus::ALL;
		
		$page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;

		$select_columns = [
			'id',
			'name',
			'description',
			'process_group_id',
			'status',
			'create_by',
			'created_at',
		];
		
		$query = $this->model
			->with([
				'processGroup:id,name', 
				'createdBy:id,full_name', 
				'processVersionActive:id,process_id,create_by,created_at,process_manager,followers',
				'processVersionActive.createdBy:id,full_name'
			])
			->select($select_columns);

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;
        // Áp dụng điều kiện theo tab
        $tabStatus->applyToQuery($query);
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        // Lấy danh sách trạng thái từ enum
        $statusList = ProcessStatus::cases();
        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $status->applyToQuery($countQuery);
            $counts[strtolower($status->name)] = $countQuery->count();
        }

        if ($page && $perPage) {
            $workflows = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $workflows = $query->get();
        }

		// Xử lý where với cột process_manager để những user nào có quyền mới được sử dụng
		$userId = auth()->id(); // Lấy ID của user đang đăng nhập
        $workflows = $workflows->filter(function($workflow) use ($userId) {
            $process_manager = $workflow->processVersionActive->process_manager;
            $followers = $workflow->processVersionActive->followers;
            $usersInScopeProcessManager = $this->userRepository->getUserByOptionScopeRes($process_manager, $workflow->id);
            $usersInScopeFollowers = $this->userRepository->getUserByOptionScopeRes($followers, $workflow->id);
            $userIdsInScope = array_merge(array_column($usersInScopeProcessManager, 'value'), array_column($usersInScopeFollowers, 'value'));
            return in_array($userId, $userIdsInScope);
        });
        
        return [
            'workflows' => $workflows,
            'counts' => $counts
        ];
	}

	public function showWorkflowDetailActive($workflowId)
	{
		// Định nghĩa các cột cần select để tối ưu query
		$select_column_workflow = [
			'id',
			'name',
			'description',
			'process_group_id',
			'status',
		];

		$select_column_process_version_active = [
			'id',
			'process_id',
			'form_id',
			'scope_use',
			'job_manager',
			'followers',
			'process_manager',
		];

		$select_column_form = [
			'id',
			'name',
			'description',
		];

		$select_column_field = [
			'id',
			'keyword',
			'display_name',
			'display_name_en',
			'type',
			'default_value',
			'required',
			'order',
			'min_equal',
			'max_equal',
			'stage_id',
			'placeholder',
			'placeholder_en',
			'column_width',
			'form_id',
			'not_edit',
			'multiple',
			'options',
			'object_table',
			'column_table',
			'sub_column_table',
			'parent_id',
			'is_active',
			'create_by',
		];

		$select_column_stage = [
			'id',
			'name',
		];

		// Thực hiện query một lần duy nhất với eager loading để tránh N+1 problem
		$workflow = $this->model
			->with([
				'processVersionActive' => function ($query) use ($select_column_process_version_active, $select_column_form, $select_column_field, $select_column_stage) {
					$query->select($select_column_process_version_active)
						->with([
							'form' => function ($query) use ($select_column_form) {
								$query->select($select_column_form);
							},
							'form.fields' => function ($query) use ($select_column_field) {
								$query->select($select_column_field)
									->with([
										'children' => function ($query) use ($select_column_field) {
											$query->select($select_column_field);
										}
									]);
							},
							'form.fields.stage' => function ($query) use ($select_column_stage) {
								$query->select($select_column_stage);
							},
						]);
				},
				'processVersionActive.stages',
				'processVersionActive.processTransitions',
				'processVersionActive.emailTemplates',
				'processVersionActive.syncGroups',
				'processVersionActive.actions',
				'processVersionActive.conditions',
			])
			->select($select_column_workflow)
			->find($workflowId);

		// Kiểm tra workflow có tồn tại không
		if (!$workflow) {
			return null;
		}

		// Kiểm tra processVersionActive có tồn tại không
		if (!$workflow->processVersionActive) {
			return $workflow; // Trả về workflow nhưng không có flow_transitions
		}

		// Lấy thông tin user và process version
		$user = Auth::user();
		$process_version_id = $workflow->processVersionActive->id;

		// Lấy flow transitions data
		$data_flow_transitions = $this->jobPermissionService->getFlowTransitionsForJob(null, $user, $process_version_id);

		// Gán flow_transitions vào workflow
		$workflow->flow_transitions = $data_flow_transitions['flow_transitions'];

		return $workflow;
	}
}
?>